#!/usr/bin/env python3
"""
Test script for Agent Memory service using mem0 with Ollama.

This script tests the basic functionality of mem0 with Qwen3:4b and Snowflake Arctic Embed2 models.
"""

import os
import json
import time
from mem0 import Memory

def print_separator(title):
    """Print a separator with a title."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80 + "\n")

def print_json(data):
    """Print data as formatted JSON."""
    print(json.dumps(data, indent=2, ensure_ascii=False))

def main():
    print_separator("Testing Agent Memory with Qwen3:4b and Snowflake Arctic Embed2")
    
    # Configuration for mem0
    supabase_url = "http://localhost:54321"
    supabase_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
    table_name = "memories"
    schema_name = "public"
    db_connection_string = "postgresql://postgres:postgres@localhost:54322/postgres"

    config = {
        "vector_store": {
            "provider": "supabase",
            "config": {
                "collection_name": table_name,  # Use the table name as the collection name
                "connection_string": db_connection_string,  # Use the database connection string
                "index_method": "hnsw",  # Or "ivfflat", choose based on your needs
                "index_measure": "cosine_distance",  # Corrected from "cosine"
                # "embedding_model_dims": 768,  # Replace with the actual dimension of snowflake-arctic-embed2
            }
        },
        "llm": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:4b",
                "temperature": 0,
                "max_tokens": 2000,
                "ollama_base_url": "http://localhost:11434",
            }
        },
        "embedder": {
            "provider": "ollama",
            "config": {
                "model": "nomic-embed-text:latest",
                # "embedding_dims": 768,  # Replace with the actual dimension of snowflake-arctic-embed2
                # "lmstudio_base_url": "http://localhost:1234"
                "ollama_base_url": "http://localhost:11434",
            }
        }
    }
    
    print("Initializing Memory with configuration...")
    try:
        m = Memory.from_config(config)
        print("✅ Memory initialized successfully!")
    except Exception as e:
        print(f"❌ Failed to initialize Memory: {str(e)}")
        return
    
    # Test 1: Add a simple memory
    print_separator("Test 1: Adding a simple memory")
    try:
        user_id = "test_user"
        memory_content = "I like to travel"
        
        print(f"Adding memory: '{memory_content}'")
        result = m.add(memory_content, user_id=user_id)
        print("✅ Memory added successfully!")
        print("Memory ID:", result.get("id"))
    except Exception as e:
        print(f"❌ Failed to add memory: {str(e)}")
        return
    
    # Test 2: Retrieve all memories
    print_separator("Test 2: Retrieving all memories")
    try:
        print(f"Retrieving all memories for user '{user_id}'")
        memories = m.get_all(user_id=user_id)
        print(f"✅ Retrieved {len(memories)} memories")
        print_json(memories)
    except Exception as e:
        print(f"❌ Failed to retrieve memories: {str(e)}")
    
    # Test 3: Search memories
    print_separator("Test 3: Searching memories")
    try:
        query = "北京有什么著名的博物馆？"
        print(f"Searching with query: '{query}'")
        results = m.search(query, user_id=user_id)
        print(f"✅ Search returned {len(results)} results")
        print_json(results)
    except Exception as e:
        print(f"❌ Failed to search memories: {str(e)}")
    
    # Test 4: Add a conversation memory
    print_separator("Test 4: Adding a conversation memory")
    try:
        conversation = [
            {"role": "user", "content": "我想去上海旅游，有什么推荐的地方吗？"},
            {"role": "assistant", "content": "上海有很多值得参观的地方，比如外滩、豫园、上海博物馆、迪士尼乐园等。您对哪种类型的景点更感兴趣？"},
            {"role": "user", "content": "我对历史文化类的景点比较感兴趣。"},
            {"role": "assistant", "content": "那我推荐您去豫园、上海博物馆和周边的田子坊。这些地方都能让您了解上海的历史文化。"}
        ]
        
        print("Adding conversation memory")
        result = m.add(conversation, user_id=user_id)
        print("✅ Conversation memory added successfully!")
        print("Memory ID:", result.get("id"))
    except Exception as e:
        print(f"❌ Failed to add conversation memory: {str(e)}")
    
    # Test 5: Search with related query
    print_separator("Test 5: Searching with related query")
    try:
        query = "上海有哪些文化景点？"
        print(f"Searching with query: '{query}'")
        results = m.search(query, user_id=user_id)
        print(f"✅ Search returned {len(results)} results")
        print_json(results)
    except Exception as e:
        print(f"❌ Failed to search memories: {str(e)}")
    
    print_separator("All tests completed")

if __name__ == "__main__":
    main()
