# 灵感代理 (Inspiration Agent)

基于用户交互记忆的灵感提示系统，通过异步分析用户会话，提供个性化的灵感和建议。

## 快速开始

### 前置条件

1. **Supabase**
   - 已启用 pgvector 扩展
   - 已创建 memories 表和 match_vectors 函数

2. **Ollama**
   - 已安装 Ollama（[下载地址](https://ollama.ai/download)）
   - 需要的模型：qwen3:4b（LLM）和 snowflake-arctic-embed2（嵌入模型）

### Docker 安装（推荐）

```bash
# 克隆仓库（如果尚未克隆）
git clone <repository-url>
cd agent

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 测试服务
chmod +x run_test_docker.sh
./run_test_docker.sh
```

### 本地安装

```bash
# 克隆仓库（如果尚未克隆）
git clone <repository-url>
cd agent

# 安装依赖并配置环境
chmod +x setup.sh
./setup.sh --setup-all

# 启动服务
source .venv/bin/activate
uvicorn src.main:app --reload --port 8010

# 测试服务
chmod +x run_test_local.sh
./run_test_local.sh
```

### API 使用

```python
# 添加记忆
curl -X POST "http://localhost:8010/memories" \
  -H "Content-Type: application/json" \
  -d '{"messages":[{"role":"user","content":"我喜欢在北京的故宫博物院参观中国古代文物。"}],"user_id":"test_user"}'

# 搜索记忆
curl -X POST "http://localhost:8010/memories/search" \
  -H "Content-Type: application/json" \
  -d '{"query":"北京有什么著名的博物馆？","user_id":"test_user"}'
```

## 功能特点

- **记忆存储与分析**：存储用户交互，区分长短期记忆，提供检索和关联能力
- **异步灵感生成**：后台分析用户历史，识别兴趣模式，提供个性化建议
- **本地模型支持**：使用 Ollama 提供的 qwen3:4b 和 snowflake-arctic-embed2 模型
- **模型同步**：宿主机和容器共享模型文件，避免重复下载
- **Supabase 向量存储**：利用 pgvector 进行高效的语义搜索

## 主要 API 端点

- `POST /memories` - 添加新的记忆
- `GET /memories/{user_id}` - 获取用户的所有记忆
- `POST /memories/search` - 搜索相关记忆
- `PUT /memories` - 更新现有记忆
- `DELETE /memories/{memory_id}` - 删除特定记忆

## 文档

- API 文档：http://localhost:8010/docs
- ReDoc：http://localhost:8010/redoc

## 技术细节

### 系统架构

本项目采用混合架构，将 Memory 功能作为独立服务实现，同时提供与主系统集成的接口。

- **Memory 服务**：使用 FastAPI 实现的独立服务
- **向量存储**：使用 Supabase 的 pgvector 扩展
- **LLM 和嵌入**：使用 Ollama 提供的本地模型

### 数据模型

Memory 数据结构：
```
id: TEXT PRIMARY KEY
embedding: VECTOR(1024)
metadata: JSONB
created_at: TIMESTAMP
updated_at: TIMESTAMP
```

### 模型配置

- **LLM**：qwen3:4b
  - 温度：0
  - 最大 token：2000
  
- **嵌入模型**：snowflake-arctic-embed2
  - 向量维度：1024

## 测试

提供了两个测试脚本：

- `run_test_local.sh`：在本地环境测试
- `run_test_docker.sh`：在 Docker 环境测试

测试内容包括：添加记忆、检索记忆、搜索记忆等基本功能。

## 下一步计划

1. 完善记忆分析算法
2. 增强灵感生成能力
3. 开发前端动画界面组件
4. 实现与主系统的集成
5. 添加用户反馈机制
6. 进行性能优化
