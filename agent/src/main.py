"""
Agent Memory Service

This service provides memory management for the agent system using mem0.
It handles storing, retrieving, and analyzing user interactions.
"""

import os
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from uuid import UUID

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Import mem0 components
from mem0 import Memory

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Agent Memory Service",
    description="Memory management for the agent system using mem0",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Update this in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize mem0 Memory
try:
    # Check if we should use local or remote configuration
    if os.getenv("MEM0_USE_LOCAL", "true").lower() == "true":
        # Local configuration with Supabase
        # Choose embedding model: nomic-embed-text (768 dims) or snowflake-arctic-embed2 (1024 dims)
        embedding_model = os.getenv("EMBEDDER_MODEL", "nomic-embed-text:latest")
        embedding_dims = 1024 if embedding_model == "snowflake-arctic-embed2" else 768

        config = {
            "vector_store": {
                "provider": "supabase",
                "config": {
                    "collection_name": "memories",
                    "connection_string": os.getenv("SUPABASE_DB_STRING", "postgresql://postgres:postgres@localhost:54322/postgres"),
                    "index_method": "hnsw",
                    "index_measure": "cosine_distance",
                    "embedding_model_dims": embedding_dims,
                }
            },
            "llm": {
                "provider": os.getenv("LLM_PROVIDER", "ollama"),
                "config": {
                    "model": os.getenv("LLM_MODEL", "qwen3:4b"),
                    "temperature": 0,
                    "max_tokens": 2000,
                    "ollama_base_url": os.getenv("OLLAMA_BASE_URL", "http://localhost:11434"),
                }
            },
            "embedder": {
                "provider": os.getenv("EMBEDDER_PROVIDER", "ollama"),
                "config": {
                    "model": embedding_model,
                    "ollama_base_url": os.getenv("OLLAMA_BASE_URL", "http://localhost:11434"),
                }
            },
        }
        memory = Memory.from_config(config)
    else:
        # Use mem0 platform with API key
        memory = Memory()

    logger.info("Memory service initialized successfully with Supabase vector store")
except Exception as e:
    logger.error(f"Failed to initialize memory service: {str(e)}")
    memory = None


# Pydantic models
class Message(BaseModel):
    role: str
    content: str


class MemoryInput(BaseModel):
    messages: List[Message]
    user_id: str
    metadata: Optional[Dict[str, Any]] = None
    infer: bool = True


class MemorySearchInput(BaseModel):
    query: str
    user_id: str
    limit: Optional[int] = 5


class MemoryUpdateInput(BaseModel):
    memory_id: str
    data: str


class MemoryResponse(BaseModel):
    id: str
    success: bool
    message: str


class MemorySearchResponse(BaseModel):
    memories: List[Dict[str, Any]]
    count: int


# Dependency to check if memory service is available
async def get_memory():
    if memory is None:
        raise HTTPException(
            status_code=503,
            detail="Memory service is not available. Check server logs for details."
        )
    return memory


# Background task for memory analysis
def analyze_memory(user_id: str):
    """
    Analyze user memories to generate insights.
    This runs as a background task.
    """
    try:
        logger.info(f"Starting memory analysis for user {user_id}")
        # TODO: Implement memory analysis logic
        # This would involve:
        # 1. Retrieving relevant memories
        # 2. Analyzing patterns and extracting insights
        # 3. Storing insights for later retrieval
        logger.info(f"Completed memory analysis for user {user_id}")
    except Exception as e:
        logger.error(f"Error in memory analysis for user {user_id}: {str(e)}")


# API endpoints
@app.get("/")
async def root():
    """Health check endpoint"""
    return {"status": "ok", "service": "Agent Memory"}


@app.post("/memories", response_model=MemoryResponse)
async def add_memory(
    memory_input: MemoryInput,
    background_tasks: BackgroundTasks,
    mem: Memory = Depends(get_memory)
):
    """
    Add a new memory from user interaction
    """
    try:
        # Convert Pydantic messages to dict format expected by mem0
        messages = [msg.model_dump() for msg in memory_input.messages]

        # Add memory using mem0
        result = mem.add(
            messages,
            user_id=memory_input.user_id,
            metadata=memory_input.metadata or {},
            infer=memory_input.infer
        )

        # Schedule background analysis
        background_tasks.add_task(analyze_memory, memory_input.user_id)

        return {
            "id": result.get("id", "unknown"),
            "success": True,
            "message": "Memory added successfully"
        }
    except Exception as e:
        logger.error(f"Error adding memory: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to add memory: {str(e)}")


@app.get("/memories/{user_id}", response_model=MemorySearchResponse)
async def get_all_memories(
    user_id: str,
    mem: Memory = Depends(get_memory)
):
    """
    Get all memories for a user
    """
    try:
        memories = mem.get_all(user_id=user_id)
        return {
            "memories": memories,
            "count": len(memories)
        }
    except Exception as e:
        logger.error(f"Error retrieving memories: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve memories: {str(e)}")


@app.post("/memories/search", response_model=MemorySearchResponse)
async def search_memories(
    search_input: MemorySearchInput,
    mem: Memory = Depends(get_memory)
):
    """
    Search for relevant memories
    """
    try:
        memories = mem.search(
            query=search_input.query,
            user_id=search_input.user_id,
            limit=search_input.limit
        )
        return {
            "memories": memories,
            "count": len(memories)
        }
    except Exception as e:
        logger.error(f"Error searching memories: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to search memories: {str(e)}")


@app.put("/memories", response_model=MemoryResponse)
async def update_memory(
    update_input: MemoryUpdateInput,
    mem: Memory = Depends(get_memory)
):
    """
    Update an existing memory
    """
    try:
        result = mem.update(
            memory_id=update_input.memory_id,
            data=update_input.data
        )
        return {
            "id": update_input.memory_id,
            "success": True,
            "message": "Memory updated successfully"
        }
    except Exception as e:
        logger.error(f"Error updating memory: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update memory: {str(e)}")


@app.delete("/memories/{memory_id}", response_model=MemoryResponse)
async def delete_memory(
    memory_id: str,
    mem: Memory = Depends(get_memory)
):
    """
    Delete a specific memory
    """
    try:
        mem.delete(memory_id=memory_id)
        return {
            "id": memory_id,
            "success": True,
            "message": "Memory deleted successfully"
        }
    except Exception as e:
        logger.error(f"Error deleting memory: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete memory: {str(e)}")


@app.delete("/memories/user/{user_id}", response_model=MemoryResponse)
async def delete_all_user_memories(
    user_id: str,
    mem: Memory = Depends(get_memory)
):
    """
    Delete all memories for a user
    """
    try:
        mem.delete_all(user_id=user_id)
        return {
            "id": user_id,
            "success": True,
            "message": f"All memories for user {user_id} deleted successfully"
        }
    except Exception as e:
        logger.error(f"Error deleting user memories: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete user memories: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8011, reload=True)
