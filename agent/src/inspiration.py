"""
Inspiration Agent Module

This module provides functionality to generate inspirational suggestions
based on analyzed user memories and interactions.
"""

import logging
import random
from typing import Dict, List, Any, Optional
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class InspirationAgent:
    """
    Generates inspirational suggestions based on user memories and interactions.
    """
    
    def __init__(self, memory_client, llm_client=None):
        """
        Initialize the inspiration agent.
        
        Args:
            memory_client: An instance of mem0 Memory client
            llm_client: Optional LLM client for generating suggestions
        """
        self.memory = memory_client
        self.llm = llm_client
    
    async def generate_inspiration(
        self, 
        user_id: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate an inspirational suggestion for the user.
        
        Args:
            user_id: The ID of the user
            context: Optional context about the user's current activity
            
        Returns:
            A dictionary containing the inspiration details
        """
        try:
            # Get relevant memories
            query = self._build_query(context)
            memories = self.memory.search(query=query, user_id=user_id, limit=5)
            
            if not memories:
                logger.info(f"No relevant memories found for user {user_id}")
                return self._generate_generic_inspiration()
            
            logger.info(f"Generating inspiration based on {len(memories)} memories for user {user_id}")
            
            # Generate inspiration based on memories
            inspiration = self._generate_personalized_inspiration(memories, context)
            
            return inspiration
            
        except Exception as e:
            logger.error(f"Error generating inspiration for user {user_id}: {str(e)}")
            return self._generate_generic_inspiration()
    
    def _build_query(self, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Build a search query based on the current context.
        
        Args:
            context: Optional context about the user's current activity
            
        Returns:
            A search query string
        """
        if not context:
            return "What are the user's main interests and preferences?"
        
        # Extract relevant information from context
        current_topic = context.get("topic", "")
        current_activity = context.get("activity", "")
        
        # Build query based on context
        if current_topic and current_activity:
            return f"What does the user know about {current_topic} and how do they approach {current_activity}?"
        elif current_topic:
            return f"What does the user know about {current_topic} and what are they interested in learning?"
        elif current_activity:
            return f"How does the user typically approach {current_activity} and what are their preferences?"
        
        return "What are the user's main interests and preferences?"
    
    def _generate_personalized_inspiration(
        self, 
        memories: List[Dict[str, Any]], 
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate a personalized inspiration based on memories and context.
        
        Args:
            memories: List of relevant memories
            context: Optional context about the user's current activity
            
        Returns:
            A dictionary containing the personalized inspiration
        """
        # This is a placeholder implementation
        # In a real system, you would use an LLM to generate personalized inspiration
        
        # Extract memory content
        memory_contents = [m.get("content", "") for m in memories if "content" in m]
        memory_ids = [m.get("id", "") for m in memories if "id" in m]
        
        # Generate a simple inspiration based on the number of memories
        if len(memory_contents) >= 3:
            content = "Based on your past interactions, you might want to explore connections between your different areas of interest."
            inspiration_type = "connection"
        elif len(memory_contents) >= 1:
            content = "You've shown interest in this topic before. Consider revisiting some of your earlier thoughts."
            inspiration_type = "reminder"
        else:
            return self._generate_generic_inspiration()
        
        return {
            "type": inspiration_type,
            "content": content,
            "confidence": 0.7,
            "related_memories": memory_ids[:3],
            "created_at": datetime.now().isoformat(),
            "animation_state": self._determine_animation_state(inspiration_type)
        }
    
    def _generate_generic_inspiration(self) -> Dict[str, Any]:
        """
        Generate a generic inspiration when no relevant memories are available.
        
        Returns:
            A dictionary containing a generic inspiration
        """
        generic_inspirations = [
            {
                "type": "suggestion",
                "content": "Consider taking a different approach to your current task.",
                "confidence": 0.5,
                "animation_state": "thinking"
            },
            {
                "type": "question",
                "content": "Have you thought about the problem from multiple perspectives?",
                "confidence": 0.5,
                "animation_state": "curious"
            },
            {
                "type": "reminder",
                "content": "Taking short breaks can help improve creativity and problem-solving.",
                "confidence": 0.5,
                "animation_state": "helpful"
            }
        ]
        
        inspiration = random.choice(generic_inspirations)
        inspiration["created_at"] = datetime.now().isoformat()
        inspiration["related_memories"] = []
        
        return inspiration
    
    def _determine_animation_state(self, inspiration_type: str) -> str:
        """
        Determine the appropriate animation state based on the inspiration type.
        
        Args:
            inspiration_type: The type of inspiration
            
        Returns:
            An animation state string
        """
        animation_states = {
            "suggestion": "excited",
            "question": "curious",
            "connection": "thoughtful",
            "reminder": "helpful"
        }
        
        return animation_states.get(inspiration_type, "neutral")
